<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Etsy Print-on-Demand Trend Dashboard</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .dashboard {
            padding: 30px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        
        .stat-card h3 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .stat-card p {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .tools-section {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
        }
        
        .tools-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .tool-card {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-left: 5px solid #00b894;
        }
        
        .tool-card h3 {
            color: #2d3436;
            margin-bottom: 15px;
            font-size: 1.3em;
        }
        
        .tool-card p {
            color: #636e72;
            margin-bottom: 15px;
            line-height: 1.6;
        }
        
        .tool-link {
            display: inline-block;
            background: #00b894;
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .tool-link:hover {
            background: #00a085;
            transform: translateY(-2px);
        }
        
        .keyword-analyzer {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .input-group {
            margin-bottom: 20px;
        }
        
        .input-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #2d3436;
        }
        
        .input-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
        }
        
        .input-group input:focus {
            outline: none;
            border-color: #74b9ff;
        }
        
        .analyze-btn {
            background: linear-gradient(135deg, #fd79a8, #e84393);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .analyze-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }
        
        .results {
            margin-top: 30px;
        }
        
        .keyword-result {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 15px;
            border-left: 4px solid #00b894;
        }
        
        .keyword-result h4 {
            color: #2d3436;
            margin-bottom: 10px;
        }
        
        .score-bar {
            background: #ddd;
            height: 8px;
            border-radius: 4px;
            overflow: hidden;
            margin: 5px 0;
        }
        
        .score-fill {
            height: 100%;
            background: linear-gradient(90deg, #00b894, #55efc4);
            transition: width 0.3s ease;
        }
        
        .tips-section {
            background: linear-gradient(135deg, #fdcb6e, #e17055);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-top: 30px;
        }
        
        .tips-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .tip-card {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }
        
        .tip-card h4 {
            margin-bottom: 10px;
            font-size: 1.2em;
        }
        
        .tip-card p {
            opacity: 0.9;
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 Etsy Print-on-Demand Trend Dashboard</h1>
            <p>USA Bazlı Trend Analizi ve Düşük Rekabet Keyword Bulucu</p>
        </div>
        
        <div class="dashboard">
            <div class="stats-grid">
                <div class="stat-card">
                    <h3 id="totalKeywords">0</h3>
                    <p>Analiz Edilen Keyword</p>
                </div>
                <div class="stat-card">
                    <h3 id="highOpportunity">0</h3>
                    <p>Yüksek Fırsat Skoru</p>
                </div>
                <div class="stat-card">
                    <h3 id="lowCompetition">0</h3>
                    <p>Düşük Rekabet</p>
                </div>
                <div class="stat-card">
                    <h3 id="trendingNow">0</h3>
                    <p>Şu An Trend</p>
                </div>
            </div>
            
            <div class="tools-section">
                <h2 style="margin-bottom: 20px; color: #2d3436;">🛠️ Ücretsiz Trend Analiz Araçları</h2>
                <div class="tools-grid">
                    <div class="tool-card">
                        <h3>🔍 Google Trends</h3>
                        <p>USA bazlı arama trendlerini analiz edin. Print-on-demand için en güvenilir kaynak.</p>
                        <a href="https://trends.google.com/trends/" target="_blank" class="tool-link">Analiz Et</a>
                    </div>
                    
                    <div class="tool-card">
                        <h3>📌 Pinterest Trends</h3>
                        <p>Görsel trendleri keşfedin. Print-on-demand tasarımları için mükemmel.</p>
                        <a href="https://trends.pinterest.com/" target="_blank" class="tool-link">Keşfet</a>
                    </div>
                    
                    <div class="tool-card">
                        <h3>📊 eRank (Ücretsiz)</h3>
                        <p>Etsy'ye özel keyword araştırması ve rekabet analizi.</p>
                        <a href="https://erank.com/" target="_blank" class="tool-link">Başla</a>
                    </div>
                    
                    <div class="tool-card">
                        <h3>🎵 TikTok Trends</h3>
                        <p>Viral olan hashtag'leri takip edin. Genç kitle için ideal.</p>
                        <a href="https://www.tiktok.com/discover" target="_blank" class="tool-link">Keşfet</a>
                    </div>
                </div>
            </div>
            
            <div class="keyword-analyzer">
                <h2 style="margin-bottom: 20px; color: #2d3436;">🎯 Hızlı Keyword Analizi</h2>
                
                <div class="input-group">
                    <label for="keywordInput">Analiz Edilecek Keyword:</label>
                    <input type="text" id="keywordInput" placeholder="Örn: funny cat shirt, mom life, coffee lover">
                </div>
                
                <button class="analyze-btn" onclick="analyzeKeyword()">🚀 Analiz Et</button>
                
                <div class="results" id="results" style="display: none;">
                    <h3 style="margin-bottom: 15px; color: #2d3436;">📊 Analiz Sonuçları</h3>
                    <div id="keywordResults"></div>
                </div>
            </div>
            
            <div class="tips-section">
                <h2>💡 Pro İpuçları</h2>
                <div class="tips-grid">
                    <div class="tip-card">
                        <h4>🎯 Niche Targeting</h4>
                        <p>Geniş keyword'ler yerine spesifik niche'leri hedefleyin. "Dog mom coffee lover" gibi.</p>
                    </div>
                    
                    <div class="tip-card">
                        <h4>📅 Mevsimsel Planlama</h4>
                        <p>Tatil ve özel günleri 2-3 ay önceden planlayın. Rekabet düşükken hazır olun.</p>
                    </div>
                    
                    <div class="tip-card">
                        <h4>🔄 Trend Rotasyonu</h4>
                        <p>Trendler hızla değişir. Haftalık analiz yapın ve portföyünüzü güncelleyin.</p>
                    </div>
                    
                    <div class="tip-card">
                        <h4>📱 Sosyal Medya Takibi</h4>
                        <p>TikTok, Instagram Reels'i takip edin. Viral olan içerikler print-on-demand fırsatı.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // Simulated data for demo
        let keywordDatabase = [
            {keyword: "funny cat shirt", trend: 85, competition: 3, opportunity: 28.3},
            {keyword: "dog mom coffee", trend: 92, competition: 2, opportunity: 46.0},
            {keyword: "sarcastic teacher", trend: 78, competition: 4, opportunity: 19.5},
            {keyword: "vintage aesthetic", trend: 88, competition: 6, opportunity: 14.7},
            {keyword: "minimalist quote", trend: 76, competition: 3, opportunity: 25.3}
        ];
        
        function updateStats() {
            document.getElementById('totalKeywords').textContent = keywordDatabase.length;
            document.getElementById('highOpportunity').textContent = keywordDatabase.filter(k => k.opportunity > 20).length;
            document.getElementById('lowCompetition').textContent = keywordDatabase.filter(k => k.competition < 4).length;
            document.getElementById('trendingNow').textContent = keywordDatabase.filter(k => k.trend > 80).length;
        }
        
        function analyzeKeyword() {
            const keyword = document.getElementById('keywordInput').value.trim();
            if (!keyword) {
                alert('Lütfen bir keyword girin!');
                return;
            }
            
            // Simulate analysis
            const trend = Math.floor(Math.random() * 40) + 60; // 60-100
            const competition = Math.floor(Math.random() * 8) + 1; // 1-8
            const opportunity = Math.round((trend * 2) / (competition + 1) * 10) / 10;
            
            const result = {
                keyword: keyword,
                trend: trend,
                competition: competition,
                opportunity: opportunity
            };
            
            displayResult(result);
            document.getElementById('results').style.display = 'block';
        }
        
        function displayResult(result) {
            const resultsDiv = document.getElementById('keywordResults');
            
            const trendColor = result.trend > 80 ? '#00b894' : result.trend > 60 ? '#fdcb6e' : '#e17055';
            const competitionColor = result.competition < 4 ? '#00b894' : result.competition < 6 ? '#fdcb6e' : '#e17055';
            const opportunityColor = result.opportunity > 20 ? '#00b894' : result.opportunity > 10 ? '#fdcb6e' : '#e17055';
            
            resultsDiv.innerHTML = `
                <div class="keyword-result">
                    <h4>"${result.keyword}"</h4>
                    
                    <div style="margin: 10px 0;">
                        <strong>📈 Trend Skoru: ${result.trend}/100</strong>
                        <div class="score-bar">
                            <div class="score-fill" style="width: ${result.trend}%; background: ${trendColor};"></div>
                        </div>
                    </div>
                    
                    <div style="margin: 10px 0;">
                        <strong>🎯 Rekabet Skoru: ${result.competition}/10</strong>
                        <div class="score-bar">
                            <div class="score-fill" style="width: ${result.competition * 10}%; background: ${competitionColor};"></div>
                        </div>
                    </div>
                    
                    <div style="margin: 10px 0;">
                        <strong>⭐ Fırsat Skoru: ${result.opportunity}</strong>
                        <div class="score-bar">
                            <div class="score-fill" style="width: ${Math.min(result.opportunity * 2, 100)}%; background: ${opportunityColor};"></div>
                        </div>
                    </div>
                    
                    <div style="margin-top: 15px; padding: 10px; background: rgba(0,184,148,0.1); border-radius: 5px;">
                        <strong>💡 Öneri:</strong> 
                        ${result.opportunity > 20 ? 'Mükemmel fırsat! Hemen tasarım yapın.' : 
                          result.opportunity > 10 ? 'İyi fırsat. Denemeye değer.' : 
                          'Rekabet yüksek. Daha spesifik keyword deneyin.'}
                    </div>
                </div>
            `;
        }
        
        // Initialize stats
        updateStats();
        
        // Enter key support
        document.getElementById('keywordInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                analyzeKeyword();
            }
        });
    </script>
</body>
</html>

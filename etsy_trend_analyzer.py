#!/usr/bin/env python3
"""
Etsy Print-on-Demand Trend & Keyword Analyzer
GERÇEK VERİLERLE çalışan trend analizi aracı
"""

import requests
import json
import time
import csv
from datetime import datetime, timedelta
import re
from urllib.parse import quote, urlencode
import xml.etree.ElementTree as ET
from bs4 import BeautifulSoup
import urllib.request

class EtsyTrendAnalyzer:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        self.keywords_data = []

    def get_real_etsy_data(self, keyword):
        """GERÇEK Etsy verilerini çek"""
        try:
            # Etsy arama URL'si
            search_url = f"https://www.etsy.com/search"
            params = {
                'q': keyword,
                'ref': 'search_bar'
            }

            response = self.session.get(search_url, params=params, timeout=10)

            if response.status_code == 200:
                # Sayfa içeriğini analiz et
                soup = BeautifulSoup(response.text, 'html.parser')

                # Sonuç sayısını bul
                results_text = soup.find('span', {'data-test-id': 'search-results-count-desktop'})
                if results_text:
                    # "X,XXX results" formatından sayıyı çıkar
                    result_count = re.findall(r'[\d,]+', results_text.text)
                    if result_count:
                        count = int(result_count[0].replace(',', ''))
                        print(f"📊 '{keyword}' için {count:,} sonuç bulundu")
                        return count

                # Alternatif yöntem - sayfa başlığından
                title = soup.find('title')
                if title and 'results' in title.text.lower():
                    numbers = re.findall(r'[\d,]+', title.text)
                    if numbers:
                        count = int(numbers[0].replace(',', ''))
                        return count

                print(f"⚠️ '{keyword}' için sonuç sayısı bulunamadı")
                return 0

        except Exception as e:
            print(f"❌ Etsy veri hatası ({keyword}): {e}")
            return 0

    def get_google_suggest_data(self, keyword):
        """GERÇEK Google arama önerilerini çek"""
        try:
            # Google Suggest API (ücretsiz ve legal)
            suggest_url = "http://suggestqueries.google.com/complete/search"
            params = {
                'client': 'firefox',
                'q': keyword
            }

            response = self.session.get(suggest_url, params=params, timeout=5)

            if response.status_code == 200:
                # JSON response parse et
                suggestions = json.loads(response.text)
                if len(suggestions) > 1:
                    return suggestions[1]  # Öneri listesi

        except Exception as e:
            print(f"❌ Google Suggest hatası: {e}")

        return []
    
    def get_real_pinterest_data(self, keyword):
        """GERÇEK Pinterest verilerini çek"""
        try:
            # Pinterest arama URL'si
            pinterest_url = f"https://www.pinterest.com/search/pins/"
            params = {'q': keyword}

            response = self.session.get(pinterest_url, params=params, timeout=10)

            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')

                # Pin sayısını bul
                pins = soup.find_all('div', {'data-test-id': 'pin'})
                pin_count = len(pins)

                # Engagement göstergelerini say
                likes = soup.find_all('div', class_=re.compile('.*like.*'))
                saves = soup.find_all('div', class_=re.compile('.*save.*'))

                engagement_score = len(likes) + len(saves)

                print(f"📌 Pinterest: '{keyword}' - {pin_count} pin, {engagement_score} engagement")
                return {
                    'pin_count': pin_count,
                    'engagement': engagement_score,
                    'popularity_score': min(pin_count * 2 + engagement_score, 100)
                }

        except Exception as e:
            print(f"❌ Pinterest hatası: {e}")

        return {'pin_count': 0, 'engagement': 0, 'popularity_score': 0}
    
    def get_real_etsy_suggestions(self, base_keyword):
        """GERÇEK Etsy arama önerilerini topla"""
        suggestions = []

        try:
            # Etsy arama sayfasına git ve önerileri çek
            search_url = "https://www.etsy.com/search"

            # Alfabetik kombinasyonlar ile gerçek öneriler al
            alphabet = 'abcdefghijklmnopqrstuvwxyz'

            for letter in alphabet[:10]:  # İlk 10 harf
                query = f"{base_keyword} {letter}"

                try:
                    params = {'q': query}
                    response = self.session.get(search_url, params=params, timeout=5)

                    if response.status_code == 200:
                        soup = BeautifulSoup(response.text, 'html.parser')

                        # Arama önerilerini bul
                        suggestion_elements = soup.find_all('span', class_=re.compile('.*suggestion.*'))

                        for elem in suggestion_elements:
                            suggestion_text = elem.get_text().strip()
                            if suggestion_text and len(suggestion_text) > 3:
                                suggestions.append(suggestion_text)

                        # Rate limiting
                        time.sleep(0.5)

                except Exception as e:
                    print(f"⚠️ Harf '{letter}' için öneri alınamadı: {e}")
                    continue

            # Google Suggest'ten de al
            google_suggestions = self.get_google_suggest_data(base_keyword)
            suggestions.extend(google_suggestions)

            # Duplikatları temizle
            unique_suggestions = list(set(suggestions))

            print(f"💡 '{base_keyword}' için {len(unique_suggestions)} gerçek öneri bulundu")
            return unique_suggestions[:25]  # İlk 25 öneri

        except Exception as e:
            print(f"❌ Etsy önerisi hatası: {e}")
            return []
    
    def calculate_real_competition_score(self, keyword):
        """GERÇEK rekabet skorunu hesapla (Etsy verilerine dayalı)"""
        try:
            # Etsy'den gerçek sonuç sayısını al
            result_count = self.get_real_etsy_data(keyword)

            # None kontrolü
            if result_count is None:
                result_count = 0

            # Rekabet skorunu hesapla (1-10 arası, düşük = iyi)
            if result_count == 0:
                competition_score = 1.0  # Hiç sonuç yok = çok düşük rekabet
            elif result_count < 100:
                competition_score = 2.0  # Çok düşük rekabet
            elif result_count < 500:
                competition_score = 3.0  # Düşük rekabet
            elif result_count < 1000:
                competition_score = 4.0  # Orta-düşük rekabet
            elif result_count < 5000:
                competition_score = 5.0  # Orta rekabet
            elif result_count < 10000:
                competition_score = 6.0  # Orta-yüksek rekabet
            elif result_count < 25000:
                competition_score = 7.0  # Yüksek rekabet
            elif result_count < 50000:
                competition_score = 8.0  # Çok yüksek rekabet
            elif result_count < 100000:
                competition_score = 9.0  # Aşırı yüksek rekabet
            else:
                competition_score = 10.0  # Maksimum rekabet

            # Niche kelime bonusu (gerçek analiz)
            niche_words = ['vintage', 'minimalist', 'boho', 'aesthetic', 'cottagecore',
                          'dark academia', 'goblincore', 'cottagecore', 'grandmillennial']
            niche_bonus = sum(0.5 for word in niche_words if word in keyword.lower())

            # Long-tail bonus
            word_count = len(keyword.split())
            longtail_bonus = max(0, (word_count - 2) * 0.3)

            # Final skor hesaplama
            final_score = max(1.0, competition_score - niche_bonus - longtail_bonus)

            print(f"🎯 '{keyword}': {result_count:,} sonuç → Rekabet: {final_score:.1f}/10")
            return round(final_score, 1)

        except Exception as e:
            print(f"❌ Rekabet analizi hatası: {e}")
            return 5.0
    
    def get_seasonal_trends(self):
        """Mevsimsel trendleri analiz et"""
        current_month = datetime.now().month
        
        seasonal_keywords = {
            1: ['new year', 'resolution', 'winter', 'cozy'],
            2: ['valentine', 'love', 'heart', 'romantic'],
            3: ['spring', 'easter', 'fresh', 'bloom'],
            4: ['easter', 'spring break', 'pastel', 'bunny'],
            5: ['mother day', 'spring', 'flower', 'garden'],
            6: ['father day', 'summer', 'graduation', 'beach'],
            7: ['summer', 'vacation', 'beach', 'patriotic'],
            8: ['back to school', 'teacher', 'student', 'autumn prep'],
            9: ['fall', 'autumn', 'pumpkin', 'back to school'],
            10: ['halloween', 'spooky', 'orange', 'fall'],
            11: ['thanksgiving', 'grateful', 'turkey', 'autumn'],
            12: ['christmas', 'holiday', 'winter', 'gift']
        }
        
        # Gelecek 3 ay için trend önerileri
        future_trends = []
        for i in range(3):
            future_month = (current_month + i) % 12 + 1
            if future_month in seasonal_keywords:
                future_trends.extend(seasonal_keywords[future_month])
        
        return future_trends
    
    def analyze_keyword_batch_real(self, keywords):
        """GERÇEK verilerle keyword listesini analiz et"""
        results = []

        for i, keyword in enumerate(keywords):
            print(f"\n📊 GERÇEK ANALİZ ({i+1}/{len(keywords)}): {keyword}")
            print("-" * 50)

            # GERÇEK Etsy rekabet analizi
            competition = self.calculate_real_competition_score(keyword)

            # GERÇEK Pinterest analizi
            pinterest_data = self.get_real_pinterest_data(keyword)

            # Google Suggest popülerlik skoru
            google_suggestions = self.get_google_suggest_data(keyword)
            google_popularity = len(google_suggestions) * 10  # Her öneri 10 puan

            # Sonuçları birleştir
            etsy_results = self.get_real_etsy_data(keyword)
            if etsy_results is None:
                etsy_results = 0

            result = {
                'keyword': keyword,
                'etsy_results': etsy_results,
                'competition_score': competition,
                'pinterest_pins': pinterest_data['pin_count'],
                'pinterest_engagement': pinterest_data['engagement'],
                'pinterest_popularity': pinterest_data['popularity_score'],
                'google_suggestions': len(google_suggestions),
                'google_popularity': min(google_popularity, 100),
                'opportunity_score': 0,
                'analysis_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }

            # GERÇEK fırsat skoru hesapla
            # Formül: (Pinterest + Google popülerlik) / (Rekabet + 1)
            if result['pinterest_popularity'] > 0 or result['google_popularity'] > 0:
                popularity_total = result['pinterest_popularity'] + result['google_popularity']
                result['opportunity_score'] = round(
                    popularity_total / (result['competition_score'] + 1), 2
                )

            # Sonuç özeti
            etsy_count = result['etsy_results'] if result['etsy_results'] is not None else 0
            print(f"✅ Etsy Sonuçlar: {etsy_count:,}")
            print(f"✅ Rekabet Skoru: {result['competition_score']}/10")
            print(f"✅ Pinterest Popülerlik: {result['pinterest_popularity']}/100")
            print(f"✅ Google Popülerlik: {result['google_popularity']}/100")
            print(f"⭐ FIRSAT SKORU: {result['opportunity_score']}")

            results.append(result)

            # Rate limiting - gerçek sitelerle çalışırken önemli
            time.sleep(2)

        return results
    
    def save_results(self, results, filename='GERCEK_etsy_analiz.csv'):
        """GERÇEK sonuçları CSV dosyasına kaydet"""
        try:
            with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = [
                    'keyword', 'etsy_results', 'competition_score',
                    'pinterest_pins', 'pinterest_engagement', 'pinterest_popularity',
                    'google_suggestions', 'google_popularity', 'opportunity_score',
                    'analysis_date'
                ]
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

                writer.writeheader()
                for result in results:
                    writer.writerow(result)

            print(f"✅ GERÇEK sonuçlar kaydedildi: {filename}")

        except Exception as e:
            print(f"❌ Kaydetme hatası: {e}")

    def get_top_opportunities(self, results, limit=10):
        """En iyi fırsatları listele (gerçek verilere göre)"""
        sorted_results = sorted(results, key=lambda x: x.get('opportunity_score', 0), reverse=True)
        return sorted_results[:limit]

def main():
    """GERÇEK VERİLERLE ana program"""
    print("🚀 GERÇEK VERİLERLE Etsy Print-on-Demand Trend Analyzer")
    print("=" * 70)
    print("⚠️  Bu araç GERÇEK verilerle çalışır - hiçbir simülasyon yok!")
    print("🌐 Etsy, Pinterest ve Google'dan canlı veri çeker")
    print("=" * 70)

    analyzer = EtsyTrendAnalyzer()

    # Kullanıcıdan keyword seçimi
    print("\n📝 Analiz seçenekleri:")
    print("1. Hızlı test (5 popüler keyword)")
    print("2. Kapsamlı analiz (20+ keyword)")
    print("3. Özel keyword listesi")

    choice = input("\nSeçiminizi yapın (1-3): ").strip()

    if choice == "1":
        # Hızlı test keyword'leri
        test_keywords = [
            'funny cat shirt',
            'dog mom coffee',
            'teacher life',
            'vintage aesthetic',
            'minimalist quote'
        ]
        keywords_to_analyze = test_keywords

    elif choice == "2":
        # Kapsamlı analiz
        base_keywords = [
            'funny shirt', 'mom life', 'coffee lover', 'dog mom', 'cat dad'
        ]

        print("\n🔍 Gerçek Etsy önerileri toplanıyor...")
        all_keywords = base_keywords.copy()

        # Her base keyword için gerçek öneriler al
        for base in base_keywords[:2]:  # İlk 2 keyword için
            print(f"📊 '{base}' için öneriler alınıyor...")
            suggestions = analyzer.get_real_etsy_suggestions(base)
            all_keywords.extend(suggestions[:5])  # Her birinden 5 öneri

        # Mevsimsel trendleri ekle
        seasonal = analyzer.get_seasonal_trends()
        all_keywords.extend([f"{s} shirt" for s in seasonal[:3]])

        # Duplikatları temizle
        keywords_to_analyze = list(set(all_keywords))[:20]

    else:
        # Özel keyword listesi
        print("\nKeyword'lerinizi virgülle ayırarak girin:")
        custom_input = input("Örn: funny cat, dog mom, coffee lover: ")
        keywords_to_analyze = [k.strip() for k in custom_input.split(',') if k.strip()]

    if not keywords_to_analyze:
        print("❌ Analiz edilecek keyword bulunamadı!")
        return

    print(f"\n📋 {len(keywords_to_analyze)} keyword GERÇEK verilerle analiz edilecek")
    print("⏱️  Bu işlem biraz zaman alabilir (gerçek sitelerden veri çekiliyor)")
    print("=" * 70)

    # GERÇEK analiz başlat
    results = analyzer.analyze_keyword_batch_real(keywords_to_analyze)

    # Sonuçları kaydet
    analyzer.save_results(results, 'GERCEK_etsy_analiz.csv')

    # En iyi fırsatları göster
    top_opportunities = analyzer.get_top_opportunities(results)

    print("\n" + "=" * 70)
    print("🏆 EN İYİ FIRSATLAR (GERÇEK VERİLERE DAYALI)")
    print("=" * 70)

    for i, opp in enumerate(top_opportunities, 1):
        print(f"\n{i:2d}. 🎯 {opp['keyword'].upper()}")
        print(f"    📊 Etsy Sonuçlar: {opp.get('etsy_results', 0):,}")
        print(f"    🎯 Rekabet: {opp.get('competition_score', 0)}/10")
        print(f"    📌 Pinterest: {opp.get('pinterest_popularity', 0)}/100")
        print(f"    🔍 Google: {opp.get('google_popularity', 0)}/100")
        print(f"    ⭐ FIRSAT SKORU: {opp.get('opportunity_score', 0)}")

        # Öneri ver
        if opp.get('opportunity_score', 0) > 20:
            print(f"    💡 ÖNERİ: 🔥 MÜKEMMEL FIRSAT! Hemen tasarım yapın!")
        elif opp.get('opportunity_score', 0) > 10:
            print(f"    💡 ÖNERİ: ✅ İyi fırsat, denemeye değer")
        else:
            print(f"    💡 ÖNERİ: ⚠️ Yüksek rekabet, dikkatli yaklaşın")

    print(f"\n✅ GERÇEK analiz tamamlandı!")
    print(f"📁 Detaylı sonuçlar: 'GERCEK_etsy_analiz.csv'")
    print(f"📊 {len([r for r in results if r.get('opportunity_score', 0) > 15])} adet yüksek fırsat bulundu!")

    print("\n💡 GERÇEK VERİ ÖNERİLERİ:")
    print("- Fırsat skoru 20+ = Hemen başlayın!")
    print("- Rekabet skoru 3 altı = Düşük rekabet")
    print("- Pinterest popülerlik 50+ = Görsel trend var")
    print("- Bu veriler gerçek zamanlı Etsy verilerine dayanır")

if __name__ == "__main__":
    main()
